$(document).ready(function() {
    
    // Mobile Navigation Toggle
    $('.hamburger').click(function() {
        $(this).toggleClass('active');
        $('.nav-menu').toggleClass('active');
        
        // Animate hamburger bars
        $('.bar').toggleClass('active');
    });

    // Close mobile menu when clicking on a link
    $('.nav-menu a').click(function() {
        $('.nav-menu').removeClass('active');
        $('.hamburger').removeClass('active');
        $('.bar').removeClass('active');
    });

    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if(target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 80
            }, 1000);
        }
    });

    // Slider Functionality
    let currentSlide = 0;
    const slides = $('.slide');
    const dots = $('.dot');
    const totalSlides = slides.length;

    function showSlide(index) {
        slides.removeClass('active');
        dots.removeClass('active');
        
        slides.eq(index).addClass('active');
        dots.eq(index).addClass('active');
    }

    function nextSlide() {
        currentSlide = (currentSlide + 1) % totalSlides;
        showSlide(currentSlide);
    }

    function prevSlide() {
        currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
        showSlide(currentSlide);
    }

    // Auto-play slider
    let sliderInterval = setInterval(nextSlide, 5000);

    // Slider controls
    $('.next-btn').click(function() {
        clearInterval(sliderInterval);
        nextSlide();
        sliderInterval = setInterval(nextSlide, 5000);
    });

    $('.prev-btn').click(function() {
        clearInterval(sliderInterval);
        prevSlide();
        sliderInterval = setInterval(nextSlide, 5000);
    });

    // Dot navigation
    $('.dot').click(function() {
        clearInterval(sliderInterval);
        currentSlide = $(this).data('slide');
        showSlide(currentSlide);
        sliderInterval = setInterval(nextSlide, 5000);
    });

    // Pause slider on hover
    $('.hero').hover(
        function() { clearInterval(sliderInterval); },
        function() { sliderInterval = setInterval(nextSlide, 5000); }
    );

    // Portfolio Filter
    $('.filter-btn').click(function() {
        const filter = $(this).data('filter');
        
        // Update active button
        $('.filter-btn').removeClass('active');
        $(this).addClass('active');
        
        // Filter portfolio items
        if (filter === 'all') {
            $('.portfolio-item').fadeIn(500);
        } else {
            $('.portfolio-item').fadeOut(300);
            setTimeout(function() {
                $('.portfolio-item[data-category="' + filter + '"]').fadeIn(500);
            }, 300);
        }
    });

    // Scroll animations
    function checkScroll() {
        $('.fade-in, .slide-in-right, .slide-in-left, .scale-in').each(function() {
            const elementTop = $(this).offset().top;
            const elementBottom = elementTop + $(this).outerHeight();
            const viewportTop = $(window).scrollTop();
            const viewportBottom = viewportTop + $(window).height();
            
            if (elementBottom > viewportTop && elementTop < viewportBottom) {
                $(this).addClass('visible');
            }
        });
    }

    // Initial check and scroll event
    checkScroll();
    $(window).scroll(checkScroll);

    // Add animation classes to elements
    $('.service-card').addClass('fade-in');
    $('.gallery-card').addClass('fade-in');
    $('.blog-card').addClass('fade-in');
    $('.portfolio-item').addClass('scale-in');
    $('.feature-card').addClass('slide-in-right');
    $('.step').addClass('fade-in');
    $('.team-member').addClass('slide-in-left');
    $('.blog-post').addClass('fade-in');

    // Statistics counter animation
    function animateCounters() {
        $('.stat-number').each(function() {
            const $this = $(this);
            const countTo = parseInt($this.text().replace(/[^\d]/g, ''));
            
            $({ countNum: 0 }).animate({
                countNum: countTo
            }, {
                duration: 2000,
                easing: 'linear',
                step: function() {
                    const suffix = $this.text().includes('+') ? '+' : '';
                    $this.text(Math.floor(this.countNum) + suffix);
                },
                complete: function() {
                    const suffix = $this.text().includes('+') ? '+' : '';
                    $this.text(countTo + suffix);
                }
            });
        });
    }

    // Trigger counter animation when statistics section is visible
    $(window).scroll(function() {
        const statsSection = $('.statistics');
        if (statsSection.length) {
            const sectionTop = statsSection.offset().top;
            const sectionBottom = sectionTop + statsSection.outerHeight();
            const viewportTop = $(window).scrollTop();
            const viewportBottom = viewportTop + $(window).height();
            
            if (sectionBottom > viewportTop && sectionTop < viewportBottom && !statsSection.hasClass('animated')) {
                statsSection.addClass('animated');
                animateCounters();
            }
        }
    });

    // Form validation and submission
    $('.contact-form, .contact-form-extended').submit(function(e) {
        e.preventDefault();
        
        const form = $(this);
        const submitBtn = form.find('button[type="submit"]');
        const originalText = submitBtn.html();
        
        // Show loading state
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...');
        submitBtn.prop('disabled', true);
        
        // Simulate form submission
        setTimeout(function() {
            // Show success message
            submitBtn.html('<i class="fas fa-check"></i> تم الإرسال بنجاح');
            submitBtn.removeClass('btn-primary').addClass('btn-success');
            
            // Reset form
            form[0].reset();
            
            // Reset button after 3 seconds
            setTimeout(function() {
                submitBtn.html(originalText);
                submitBtn.removeClass('btn-success').addClass('btn-primary');
                submitBtn.prop('disabled', false);
            }, 3000);
        }, 2000);
    });

    // Newsletter form
    $('.newsletter-form').submit(function(e) {
        e.preventDefault();
        
        const email = $(this).find('input[type="email"]').val();
        const submitBtn = $(this).find('button');
        const originalText = submitBtn.text();
        
        submitBtn.html('<span class="loading"></span> جاري الاشتراك...');
        submitBtn.prop('disabled', true);
        
        setTimeout(function() {
            submitBtn.html('تم الاشتراك بنجاح!');
            
            setTimeout(function() {
                submitBtn.html(originalText);
                submitBtn.prop('disabled', false);
            }, 3000);
        }, 2000);
    });

    // FAQ Toggle
    $('.faq-question').click(function() {
        const faqItem = $(this).parent();
        const isActive = faqItem.hasClass('active');
        
        // Close all FAQ items
        $('.faq-item').removeClass('active');
        $('.faq-answer').slideUp();
        
        // Open clicked item if it wasn't active
        if (!isActive) {
            faqItem.addClass('active');
            faqItem.find('.faq-answer').slideDown();
        }
    });

    // Navbar background on scroll
    $(window).scroll(function() {
        if ($(this).scrollTop() > 50) {
            $('.header').addClass('scrolled');
        } else {
            $('.header').removeClass('scrolled');
        }
    });

    // Back to top button
    $('body').append('<button id="back-to-top" title="العودة للأعلى"><i class="fas fa-arrow-up"></i></button>');
    
    const backToTopBtn = $('#back-to-top');
    
    backToTopBtn.css({
        'position': 'fixed',
        'bottom': '30px',
        'left': '30px',
        'width': '50px',
        'height': '50px',
        'background': '#F39C12',
        'color': 'white',
        'border': 'none',
        'border-radius': '50%',
        'cursor': 'pointer',
        'opacity': '0',
        'visibility': 'hidden',
        'transition': 'all 0.3s ease',
        'z-index': '999',
        'box-shadow': '0 4px 15px rgba(243, 156, 18, 0.3)'
    });
    
    $(window).scroll(function() {
        if ($(this).scrollTop() > 500) {
            backToTopBtn.css({
                'opacity': '1',
                'visibility': 'visible'
            });
        } else {
            backToTopBtn.css({
                'opacity': '0',
                'visibility': 'hidden'
            });
        }
    });
    
    backToTopBtn.click(function() {
        $('html, body').animate({ scrollTop: 0 }, 800);
        return false;
    });

    backToTopBtn.hover(
        function() {
            $(this).css({
                'background': '#E67E22',
                'transform': 'scale(1.1)'
            });
        },
        function() {
            $(this).css({
                'background': '#F39C12',
                'transform': 'scale(1)'
            });
        }
    );

    // Lazy loading for images
    $('img').each(function() {
        const img = $(this);
        const src = img.attr('src');
        
        if (src) {
            img.hide().load(function() {
                img.fadeIn(500);
            });
        }
    });

    // Parallax effect for hero section
    $(window).scroll(function() {
        const scrolled = $(this).scrollTop();
        const parallax = $('.hero');
        const speed = scrolled * 0.5;
        
        parallax.css('transform', 'translateY(' + speed + 'px)');
    });

    // Service card hover effects
    $('.service-card').hover(
        function() {
            $(this).find('.service-icon').addClass('bounce');
        },
        function() {
            $(this).find('.service-icon').removeClass('bounce');
        }
    );

    // Add bounce animation class
    $('<style>')
        .prop('type', 'text/css')
        .html('.bounce { animation: bounce 0.6s ease; } @keyframes bounce { 0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); } 40%, 43% { transform: translate3d(0, -30px, 0); } 70% { transform: translate3d(0, -15px, 0); } 90% { transform: translate3d(0, -4px, 0); } }')
        .appendTo('head');

    // Smooth reveal animation for sections
    $('.section-header').addClass('fade-in');
    $('.about-content').addClass('slide-in-right');
    $('.contact-content').addClass('slide-in-left');

    // Portfolio Lightbox functionality
    let currentZoom = 1;
    let isDragging = false;
    let startX, startY, translateX = 0, translateY = 0;

    $('.portfolio-item').click(function() {
        const title = $(this).find('h3').text();
        const description = $(this).find('p').text();
        const image = $(this).find('img').attr('src');
        const details = $(this).find('.portfolio-details').html() || '';

        // Use the same image for lightbox (local image is already high quality)
        const highResImage = image;

        // Define helper functions first
        function updateImageTransform() {
            $('.lightbox-image').css('transform',
                `scale(${currentZoom}) translate(${translateX}px, ${translateY}px)`);
        }

        function updateZoomButtons() {
            $('.zoom-out').prop('disabled', currentZoom <= 0.5);
            $('.zoom-in').prop('disabled', currentZoom >= 3);
        }

        function closeLightbox() {
            $('.portfolio-lightbox').removeClass('active');
            setTimeout(() => {
                $('.portfolio-lightbox').remove();
                $('body').css('overflow', 'auto');
            }, 300);
        }

        // Create enhanced lightbox modal
        const lightbox = `
            <div class="portfolio-lightbox">
                <div class="lightbox-container">
                    <div class="lightbox-header">
                        <h3 class="lightbox-title">${title}</h3>
                        <button class="lightbox-close" aria-label="إغلاق">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="lightbox-image-container">
                        <div class="lightbox-loading">
                            <i class="fas fa-spinner"></i>
                        </div>
                        <img src="${highResImage}" alt="${title}" class="lightbox-image" style="display: none;">
                        <div class="lightbox-controls">
                            <button class="zoom-btn zoom-out" aria-label="تصغير">
                                <i class="fas fa-minus"></i>
                            </button>
                            <button class="zoom-btn zoom-in" aria-label="تكبير">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="lightbox-info">
                        <p class="lightbox-description">${description}</p>
                        ${details ? `<div class="lightbox-details">${details}</div>` : ''}
                    </div>
                </div>
            </div>
        `;

        $('body').append(lightbox);

        // Prevent body scroll when modal is open
        $('body').css('overflow', 'hidden');

        // Show lightbox with animation
        setTimeout(() => {
            $('.portfolio-lightbox').addClass('active');
        }, 10);

        // Handle image loading
        $('.lightbox-image').on('load', function() {
            $('.lightbox-loading').fadeOut(300);
            $(this).fadeIn(300);
        }).on('error', function() {
            $('.lightbox-loading').html('<i class="fas fa-exclamation-triangle"></i><br>فشل في تحميل الصورة');
        });

        // Reset zoom and position
        currentZoom = 1;
        translateX = 0;
        translateY = 0;
        updateImageTransform();



        // Close button click
        $('.lightbox-close').click(closeLightbox);

        // Click outside to close
        $('.portfolio-lightbox').click(function(e) {
            if (e.target === this) {
                closeLightbox();
            }
        });

        // Escape key to close
        $(document).on('keydown.lightbox', function(e) {
            if (e.key === 'Escape' || e.keyCode === 27) {
                closeLightbox();
                $(document).off('keydown.lightbox');
            }
        });

        // Zoom functionality
        $('.zoom-in').click(function() {
            if (currentZoom < 3) {
                currentZoom += 0.5;
                updateImageTransform();
                updateZoomButtons();
            }
        });

        $('.zoom-out').click(function() {
            if (currentZoom > 0.5) {
                currentZoom -= 0.5;
                updateImageTransform();
                updateZoomButtons();
            }
        });



        // Image dragging for zoomed images
        $('.lightbox-image').on('mousedown touchstart', function(e) {
            if (currentZoom > 1) {
                isDragging = true;
                const clientX = e.type === 'mousedown' ? e.clientX : e.touches[0].clientX;
                const clientY = e.type === 'mousedown' ? e.clientY : e.touches[0].clientY;
                startX = clientX - translateX;
                startY = clientY - translateY;
                $(this).css('cursor', 'grabbing');
                e.preventDefault();
            }
        });

        $(document).on('mousemove touchmove', function(e) {
            if (isDragging && currentZoom > 1) {
                const clientX = e.type === 'mousemove' ? e.clientX : e.touches[0].clientX;
                const clientY = e.type === 'mousemove' ? e.clientY : e.touches[0].clientY;
                translateX = clientX - startX;
                translateY = clientY - startY;
                updateImageTransform();
                e.preventDefault();
            }
        });

        $(document).on('mouseup touchend', function() {
            if (isDragging) {
                isDragging = false;
                $('.lightbox-image').css('cursor', currentZoom > 1 ? 'grab' : 'default');
            }
        });

        // Mouse wheel zoom
        $('.lightbox-image-container').on('wheel', function(e) {
            e.preventDefault();
            const delta = e.originalEvent.deltaY;
            if (delta < 0 && currentZoom < 3) {
                currentZoom += 0.2;
            } else if (delta > 0 && currentZoom > 0.5) {
                currentZoom -= 0.2;
            }
            updateImageTransform();
            updateZoomButtons();
        });

        // Initialize zoom buttons
        updateZoomButtons();
    });

    // Add success button style
    $('<style>')
        .prop('type', 'text/css')
        .html('.btn-success { background: #27AE60; color: white; }')
        .appendTo('head');

    // Typewriter effect for hero titles
    function typeWriter(element, text, speed = 100) {
        let i = 0;
        element.text('');
        
        function type() {
            if (i < text.length) {
                element.text(element.text() + text.charAt(i));
                i++;
                setTimeout(type, speed);
            }
        }
        
        type();
    }

    // Apply typewriter effect to first slide title
    setTimeout(function() {
        const firstTitle = $('.slide.active .slide-title');
        const titleText = firstTitle.text();
        typeWriter(firstTitle, titleText, 80);
    }, 1000);

    // Form field focus effects
    $('.form-group input, .form-group textarea, .form-group select').focus(function() {
        $(this).parent().addClass('focused');
    }).blur(function() {
        $(this).parent().removeClass('focused');
    });

    // Add focused state styles
    $('<style>')
        .prop('type', 'text/css')
        .html('.form-group.focused label { color: #F39C12; transform: translateY(-5px); }')
        .appendTo('head');

    console.log('🎉 موقع الماسة للحدادة والألومنيوم جاهز!');
});